﻿using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;
using AutoMapper;
using RoofSnap.Core.Models;
using RoofSnap.WebAPI.Areas.Projects;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateOptions;
using RoofSnap.WebAPI.Areas.UserRoles;
using RoofSnap.WebAPI.Common;
using RoofSnap.WebAPI.Common.Auth;
using RoofSnap.WebAPI.Common.Data;
using RoofSnap.WebAPI.Common.WebAPI;
using WebApi.Hal;

namespace RoofSnap.WebAPI.Areas.Offices.Estimating.Templates
{
    [RoutePrefix("v1/estimatetemplates")]
    [EstimateTemplateAuthFilter]
    public class EstimateTemplatesController : RoofSnapApiController<EstimateTemplateRepresentation,
        EstimateTemplateListRepresentation, DbEstimateTemplate, string>
    {
        private readonly IEstimateTemplateService _estimateTemplateService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IProjectService _projectService;

        public EstimateTemplatesController(IMapper mapper,
            IEstimateTemplateService service,
            IUnitOfWork unitOfWork,
            IProjectService projectService) : base(mapper,
            service)
        {
            _estimateTemplateService = service;
            _unitOfWork = unitOfWork;
            _projectService = projectService;
        }

        protected override Link GetAllLink => EstimateTemplateLinkTemplates.EstimateTemplates;

        [HttpGet]
        [Route("")]
        [JwtOverrideAuthorize(RoofSnapRoles.Admin)]
        [ResponseType(typeof(EstimateTemplateListRepresentation))]
        [ApiExplorerSettings(IgnoreApi = true)]
        public override Task<IHttpActionResult> GetAll(string[] ids, int page = 1, string search = "")
        {
            return base.GetAll(ids, page, search);
        }

        [HttpGet]
        [Route("{id}")]
        [ResponseType(typeof(EstimateTemplateRepresentation))]
        public override IHttpActionResult GetById(string id)
        {
            return base.GetById(id);
        }

        [HttpPost]
        [Route("")]
        [ResponseType(typeof(EstimateTemplateRepresentation))]
        public async Task<IHttpActionResult> CreateEstimateTemplate(string projectId, string estimateId, CreateEstimateTemplateDto createEstimateTemplateDto)
        {
            ProjectModel project = await _projectService.Get(projectId).GetSingleResultAsync();
            if (project == null)
                return NotFound();

            var dbEstimateTemplate = Mapper.Map<DbEstimateTemplate>(createEstimateTemplateDto);

            DbEstimateTemplate newEstimateTemplate;
            try
            {
                newEstimateTemplate = await _unitOfWork.RunAsync(() => _estimateTemplateService.CreateEstimateTemplateAsync(projectId, estimateId, dbEstimateTemplate));
            }
            catch (EntityNotFoundException<DbProjectEstimateOption> e)
            {
                return BadRequest(e.Message);
            }

            var estimateTemplateRepresentation = Mapper.Map<EstimateTemplateRepresentation>(newEstimateTemplate);
            return Created($"{BaseUri}v1/estimatetemplates/{estimateTemplateRepresentation.Id}", estimateTemplateRepresentation);
        }

        [HttpPut]
        [Route("{id}")]
        [ResponseType(typeof(EstimateTemplateRepresentation))]
        public async Task<IHttpActionResult> UpdateEstimateTemplateFromEstimate(string id, string projectId, string estimateId)
        {
            DbEstimateTemplate updatedTemplate = await _unitOfWork.RunAsync(() => _estimateTemplateService.UpdateEstimateTemplateFromEstimateAsync(id, projectId, estimateId));

            if (updatedTemplate == null)
                return NotFound();

            var estimateTemplateRepresentation = Mapper.Map<EstimateTemplateRepresentation>(updatedTemplate);
            return Ok(estimateTemplateRepresentation);
        }

        [HttpDelete]
        [Route("{id}")]
        [JwtOverrideAuthorize(RoofSnapRoles.Admin, RoofSnapRoles.AccountAdmin)]
        public async Task<IHttpActionResult> DeleteEstimateTemplateAsync(string id)
        {
            DbEstimateTemplate deletedEstimate = await _unitOfWork.RunAsync(() =>
            {
                DbEstimateTemplate estimate = _estimateTemplateService.Get(id);
                if (estimate == null)
                    return null;

                _estimateTemplateService.DeleteEstimateTemplate(estimate);
                return estimate;
            });

            if (deletedEstimate == null)
                return NotFound();

            return NoContent();
        }
    }
}