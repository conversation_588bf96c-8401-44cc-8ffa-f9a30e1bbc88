export function getLastUpdatedLabel(updatedTime: Date | string | number): string {
    const updated = new Date(updatedTime);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - updated.getTime()) / 60000);

    if (diffMinutes < 1) {
        return "Last Saved: Just now";
    }

    if (diffMinutes < 60) {
        return `Last Saved: ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    }

    const pad = (n: number) => String(n).padStart(2, '0');

    const formattedDate = `${pad(updated.getMonth() + 1)}/${pad(updated.getDate())}/${updated.getFullYear()}`;
    const formattedTime = `${pad(updated.getHours())}:${pad(updated.getMinutes())}`;

    return `Last Saved: ${formattedDate} at ${formattedTime}`;
}
